const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()
const QuestionParseHtml = require("@/utils/QuestionParseHtml")
let PAGE_OPTIONS = {} // 页面进入时参数
Page({
  data: {
    noticeData: null,
    isExpanded: false,
    showToggle: false,
    maxHeight: 800, // 默认收起状态的最大高度(px)
    actualHeight: 0, // 内容实际高度
    isPageLoadComplete: false, // 页面是否加载完成
    tagStyle: {
      table:
        "border-top: 1px solid gray; border-left: 1px solid gray; border-collapse:collapse",
      th: "border-right: 1px solid gray; border-bottom: 1px solid gray;",
      td: "border-right: 1px solid gray; border-bottom: 1px solid gray;",
    },
  },
  async onLoad(options) {
    await APP.checkLoadRequest()
    PAGE_OPTIONS = options
    await this.getNoteDeatail()
    // this.checkContentHeight()
  },
  async onShow() {
    // 第一次onshow 不请求
    if (!this.data.isPageLoadComplete) {
      return
    }
    await this.getNoteDeatail()
    // this.checkContentHeight()
  },
  async getNoteDeatail() {
    const res = await UTIL.request(API.getNoteDeatail, {
      id: PAGE_OPTIONS?.id,
    })
    if (res && res.error && res.error.code === 0 && res.data) {
      console.log(res, "12312321")
      let resData = res.data
      if (resData?.content?.body_content) {
        resData.content.body_content = QuestionParseHtml.processRichTextContent(
          resData.content.body_content
        )
      }
      this.setData({
        noticeData: resData,
        isPageLoadComplete: true,
      })
    }
  },
  tapCustomerService(e) {
    const { cmd_json } = e.currentTarget.dataset.item
    console.log(e, cmd_json)
    APP.toCmdUnitKey(cmd_json)
  },
  // 展开收起
  toggleText() {
    this.setData({
      isExpanded: !this.data.isExpanded,
    })
  },
  goDetail() {
    ROUTER.navigateTo({
      path: "/pages/notice/detail/index",
      query: { id: this.data.noticeData.article_id },
    })
  },
  // 页面返回方法
  backPage() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/home/<USER>/index",
      })
      console.log("回首页")
      return false
    }
    console.log("触发返回")
    wx.navigateBack({
      delta: 1,
    })
  },
  onReachBottom() {},
  onShareAppMessage() {},
})
