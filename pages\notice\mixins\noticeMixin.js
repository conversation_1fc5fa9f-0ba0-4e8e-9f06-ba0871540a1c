/**
 * 公告-合辑详情  Mixin
 * 提供通用的地区选择功能，包括省市区三级联动、缓存管理等
 * 使用方式：在页面的 JS 文件中通过 Object.assign 混入
 */

const UTIL = require("@/utils/util")
const API = require("@/config/api")
const ROUTER = require("@/services/mpRouter")
const APP = getApp()
const { processMenuList } = require("@/services/menuServices")
const {
  setJobDetailSelectForTemplateCache,
  getJobDetailSelectForTemplateCache,
  setJobDetailPopuSelectForTemplateCache,
  getJobDetailPopuSelectForTemplateCache,
  setOfficialNewsCache,
  getOfficialNewsCache,
} = require("@/utils/cache/filterCache")

const { handleMultiSelect } = require("@/services/selectionService")

const noticeMixin = {
  noticeData: {
    // 静态tab配置（所有可能的tab）
    allTabsConfig: [
      { key: "detail", title: "公告详情", alwaysShow: true },
      { key: "position", title: "职位列表", conditionField: "job_num" },
      { key: "official", title: "官方动态", conditionField: "notice_num" },
    ],
    // 动态生成的tab列表
    tabList: ["公告详情"],
    // 当前激活的tab索引
    activeIndex: 0,
    // tab键值到索引的映射
    tabKeyToIndex: { detail: 0 },
    // 索引到tab键值的映射
    indexToTabKey: { 0: "detail" },
    isExpanded: false,
    showToggle: false,
    maxHeight: 2500, // 默认收起状态的最大高度(px)
    actualHeight: 0, // 内容实际高度
    // 分页相关状态
    hasMore: true, // 是否还有更多数据
    isLoading: false, // 是否正在加载
    jobList: [],
    officialList: [],
    activeExpanded: "",
    examPopuSelectForTemplate: {},
    examMenuData: {},
    examList: [],
    // van-popup弹窗内的地区选择弹窗控制
    showExamRegionPopup: false,
    show: false,
    activeExamExpanded: "",
    examRegionPopupTop: 0,
    // 新增字段
    contentBoxTopHeight: 120, // content-box-top高度
    selectBoxTop: 220, // select-box-content的top值
    selectBoxSticky: false, // select-box 是否处于吸顶状态
    selectBoxPopupTop: 0, // select-box 弹窗的位置
    showPopupExam: false,
    hasPageIcon: false,

    // select-box 弹窗相关数据
    showSelectBoxPopup: false,
    popuShow: false,
    // 专业弹窗
    majorShow: false,
  },
  noticeMethods: {
    // 详情筛选相关逻辑
    // initDynamicMenu(serverMenuList) {
    //   if (serverMenuList && serverMenuList.length) {
    //     // 使用提取后的纯函数处理菜单
    //     const menuList = processMenuList(serverMenuList)
    //     const hasPageIcon = menuList.some((menuGroup) =>
    //       menuGroup.some((item) => item.type === "page_icon")
    //     )
    //     const detailMenuData = {}
    //     serverMenuList.forEach((item) => {
    //       detailMenuData[item.filter_key] = item
    //     })

    //     this.setData({
    //       detailMenuData,
    //       menuList,
    //       jobDetailSelectForTemplate:
    //         this.getNoticeSelectFromMenuData(serverMenuList),
    //       hasPageIcon,
    //     })
    //   }
    // },
    /**
     * 初始化动态菜单
     * @param {Array} serverMenuList 服务器返回的菜单列表
     */
    initDynamicMenu(serverMenuList) {
      if (serverMenuList && serverMenuList.length) {
        // 使用提取后的纯函数处理菜单
        const menuList = processMenuList(serverMenuList)
        console.log(menuList, "得到的的的的的的的的的饿的额的的的的饿的额的")
        if (this.data.pageType == "collection") {
          const collectionMenuData = {}
          serverMenuList.forEach((item) => {
            collectionMenuData[item.filter_key] = item
          })
          this.setData({
            collectionMenuData,
            menuList,
            collectionSelectForTemplate: this.getNoticeSelectFromMenuData(
              serverMenuList
            ),
          })
        } else {
          const hasPageIcon = menuList.some((menuGroup) =>
            menuGroup.some((item) => item.type === "page_icon")
          )
          const detailMenuData = {}
          serverMenuList.forEach((item) => {
            detailMenuData[item.filter_key] = item
          })

          this.setData({
            detailMenuData,
            menuList,
            jobDetailSelectForTemplate: this.getNoticeSelectFromMenuData(
              serverMenuList
            ),
            hasPageIcon,
          })
        }
      }
    },
    // 处理接口请求参数
    buildApiParams(selectedData) {
      console.log(selectedData, "拿到的")
      const apiParams = {}

      Object.keys(selectedData).forEach((keyName) => {
        const data =
          selectedData[keyName] || (keyName !== "filter_list" ? [] : {})
        if (keyName === "fit_me" || keyName === "has_tenure") {
          apiParams[keyName] = data[0] || null
        }
        if (keyName === "apply_region") {
          const regionData = data
            .map((region) => {
              // 如果已经是字符串格式，直接使用
              return region.key
            })
            .filter((regionCode) => regionCode) // 过滤掉空值
          console.log(regionData, "---------------")
          apiParams["region"] = regionData
        }
        if (keyName === "article_list") {
          const regionData = data.map((region) => {
            // 如果已经是字符串格式，直接使用
            return region.id
          })
          console.log(regionData, "---------------")
          apiParams["article_list"] = regionData
        }
        if (keyName === "filter_list") {
          Object.keys(data).forEach((filterKey) => {
            const filterValue = data[filterKey] || []

            // 招录人数 - 转换为数字
            if (filterKey === "need_num") {
              const numValue = Number(filterValue[0])
              const val = !isNaN(numValue) ? numValue : null
              apiParams[filterKey] = val
            } else {
              apiParams[filterKey] = filterValue
            }
          })
        }
        if (keyName === "tmp_major") {
          console.log(data, "111111111111111111")
          // 添加更严格的空值检查
          if (data && Array.isArray(data.selectedMajorIds) && data.selectedMajorIds.length > 0) {
            apiParams[keyName] = data.selectedMajorIds[data.selectedMajorIds.length - 1]
          } else {
            apiParams[keyName] = null
          }
        } else if (keyName !== "fit_me" && keyName !== "has_tenure" && keyName !== "apply_region" && keyName !== "article_list" && keyName !== "filter_list") {
          // 只处理其他未特殊处理的字段
          apiParams[keyName] = data
        }
      })
      delete apiParams?.filter_list
      console.log("得到的参数", apiParams)
      return UTIL.convertArraysToString(apiParams)
    },

    // 获取职位列表
    async getJobList(filterConditions = {}, isLoadMore = false) {
      // 如果正在加载或没有更多数据，直接返回
      if (
        this.data.isLoading ||
        (!isLoadMore && !this.data.hasMore && this.data.page > 1)
      ) {
        return
      }

      try {
        // 确保传递的参数不为空，至少是一个空对象
        const requestParams = {
          page: this.data.page,
          ...filterConditions,
          id: this.PAGE_OPTIONS.id,
        }

        this.setData({
          isLoading: true,
        })

        const res = await UTIL.request(API.getJobList, requestParams)

        if (res && res.error && res.error.code === 0 && res.data) {
          console.log("获取职位列表成功:", res.data)

          // 处理职位列表数据
          const newList = res.data.list || []

          // 根据是否为加载更多来决定如何处理数据
          let updatedJobList
          if (isLoadMore) {
            // 分页加载：追加到现有数据
            updatedJobList = [...this.data.jobList, ...newList]
          } else {
            // 首次加载或筛选：直接使用新数据
            updatedJobList = newList
          }

          // 更新页面数据
          this.setData({
            jobList: updatedJobList,
            hasMore: newList.length > 0, // 如果返回的数据为空，说明没有更多数据
            isLoading: false,
            isRequest: true,
          })

          return res.data
        } else {
          console.error("获取职位列表失败:", res)
          this.setData({
            isLoading: false,
            hasMore: false,
          })
          return null
        }
      } catch (error) {
        console.error("请求职位列表异常:", error)
        this.setData({
          isLoading: false,
        })
        return null
      }
    },
    // 获取文章列表
    async getArticleNoticeList() {
      let requestParams = {
        id: this.PAGE_OPTIONS.id,
      }
      const res = await UTIL.request(API.getArticleNoticeList, requestParams)
      if (res && res.error && res.error.code === 0 && res.data) {
        const resData = res.data
        this.setData({
          officialList: resData.list,
        })
        console.log(resData, "------------")
      }
    },
    // 筛选菜单点击
    handleMenuClick(e) {
      const { type, currentItem } = e.detail || e.currentTarget.dataset
      const { showPopupFilterMenu, jobDetailSelectForTemplate } = this.data
      const filterKey = currentItem.filter_key
      console.log(currentItem, "----------------------------")
      if (type == "page" && filterKey == "filter_list") {
        this.hidePopupMenuFilter()
        ROUTER.navigateTo({
          path: "/pages/select/select-job/index",
          query: {
            article_id: this.PAGE_OPTIONS.id,
          },
        })
        return
      }
      const currentMenuSelected = jobDetailSelectForTemplate[filterKey]
      if (type == "dialog_major") {
        this.setData({
          majorShow: true,
        })
        return
      }

      if (type !== "apply_region") {
        this.setData({
          showRegionList: false,
        })
      }

      if (type === "check" || type === "apply_region") {
        this.hidePopupMenuFilter()
      } else if (
        showPopupFilterMenu === true &&
        this.data.activeExpanded === filterKey
      ) {
        this.hidePopupMenuFilter()
      } else {
        this.showPopupMenuFilter()
      }

      this.setData({
        activeExpanded: this.data.activeExpanded === filterKey ? "" : filterKey, // 设置当前key为展开状态
      })

      // 处理 filter_key 为 apply_region 的情况：走公告现在的逻辑
      if (filterKey === "apply_region") {
        // 切换地区列表显示状态
        this.setData({
          showRegionList: !this.data.showRegionList,
        })
        return
      }

      // 处理check类型：单选，不打开弹窗，点击选中，再次点击取消
      if (type === "check") {
        console.log(currentMenuSelected, currentItem.value, "12312312321")
        const updatedValue = handleMultiSelect(
          currentMenuSelected || [],
          currentItem.value
        )

        this.setData({
          [`jobDetailSelectForTemplate.${filterKey}`]: updatedValue,
        })

        // 检查并同步filter_list中对应的键
        if (
          this.data.jobDetailSelectForTemplate?.filter_list?.[filterKey] !==
          undefined
        ) {
          console.log(`同步 filter_list.${filterKey} 的值:`, updatedValue)
          this.setData({
            [`jobDetailSelectForTemplate.filter_list.${filterKey}`]: updatedValue,
          })
        }

        setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
        this.applyFilter()
        return
      }
    },
    // async applyFilter() {
    //   // 重置分页状态，但不清空jobList，避免缺省图闪现
    //   this.setData({
    //     page: 1,
    //     hasMore: true,
    //   })
    //   console.log(this.data.jobDetailSelectForTemplate, "12312312")
    //   const apiParams = this.buildApiParams(
    //     this.data.jobDetailSelectForTemplate
    //   )
    //   console.log("拿到的参数", apiParams)
    //   await this.getJobList(apiParams, false)
    // },
    async applyFilter() {
      // 重置分页状态，但不清空articleList，避免缺省图闪现
      this.setData({
        page: 1,
        hasMore: true,
        // articleList: [] // 注释掉，避免缺省图闪现
      })
      if (this.data.pageType == "collection") {
        const apiParams = this.buildApiParams(
          this.data.collectionSelectForTemplate
        )
        console.log("拿到的参数", apiParams)
        await this.getArticleChildList(apiParams, false)
      } else {
        const apiParams = this.buildApiParams(
          this.data.jobDetailSelectForTemplate
        )
        console.log("拿到的参数", apiParams)
        await this.getJobList(apiParams, false)
      }
    },
    // 筛选菜单确认
    handleJobMenuFilterConfirm(e) {
      const { filterKey, tempSelected } = e.detail
      // 清空展开状态
      this.setData({
        [`jobDetailSelectForTemplate.${filterKey}`]: tempSelected,
        activeExpanded: "",
      })
      setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
      this.hidePopupMenuFilter()
      this.closeSelectBoxPopup()
      this.applyFilter()
    },

    updateJobSelectForTemplateFromCache() {
      const jobDetailSelectForTemplate = this.data.jobDetailSelectForTemplate
      const cacheJobSelectForTemplate = getJobDetailSelectForTemplateCache()
      for (const key in jobDetailSelectForTemplate) {
        if (cacheJobSelectForTemplate[key]) {
          jobDetailSelectForTemplate[key] = cacheJobSelectForTemplate[key]
        }
      }
      this.setData({
        jobDetailSelectForTemplate,
      })
    },

    // 关注公告
    changeCollect() {
      this.setFollows()
    },
    async setFollows() {
      const is_follow = this.data?.noticeData?.is_follow
      let params = {
        item_type: "article",
        item_no: [this.PAGE_OPTIONS.id],
        type: is_follow == 0 ? "follow" : "unfollow",
      }
      const res = await UTIL.request(API.setFollows, params)
      if (res.error.code === 0) {
        wx.showToast({
          title: is_follow == 0 ? "关注成功" : "已取消关注",
          icon: "none",
          duration: 2000,
        })
        this.setData({
          ["noticeData.is_follow"]: is_follow == 0 ? 1 : 0,
        })
      }
      console.log(res, "123123123123")
    },
    // 打开公告弹窗
    openSelectBoxPopup() {
      console.log("打开选择公告弹窗")

      // 如果弹窗已经打开，则关闭弹窗
      if (this.data.showSelectBoxPopup) {
        this.closeSelectBoxPopup()
        return
      }

      // 如果弹窗未打开，则打开弹窗
      this.calculateSelectBoxPopupPosition()
      this.setData({
        showSelectBoxPopup: true,
        showPopupFilterMenu: false,
      })
      // 禁用页面滚动
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      })
      this.setData({
        pageScrollDisabled: true,
      })
    },
    // 关闭公告弹窗
    closeSelectBoxPopup() {
      console.log("关闭选择公告弹窗")
      this.setData({
        showSelectBoxPopup: false,
        pageScrollDisabled: false, // 恢复页面滚动
      })
    },
    // 选择公告项
    handleSelectBoxItemClick(e) {
      const { id, title } = e.currentTarget.dataset
      console.log("选择公告项:", { id, title })

      // 更新选中状态
      const updatedList = this.data.selectBoxList.map((item) => ({
        ...item,
        selected: item.id === id,
      }))

      // 更新当前选中的标题
      this.setData({
        selectBoxList: updatedList,
        currentSelectBoxTitle: title,
        showSelectBoxPopup: false,
        pageScrollDisabled: false, // 恢复页面滚动
      })
    },
    // 选项确认公用逻辑
    handlePopuMenuFilterConfirm() {
      const { filterKey, tempSelected } = e.detail
      // 清空展开状态
      this.setData({
        [`examPopuSelectForTemplate.${filterKey}`]: tempSelected,
        activeExamExpanded: "",
      })
      setJobDetailSelectForTemplateCache(this.data.examPopuSelectForTemplate)
      this.hidePopupMenu()
      this.applyPopuFilter()
    },

    // 考试列表全部职位点击
    goPosition(e) {
      this.setData({
        show: false,
      })
      const { id } = e.currentTarget.dataset.item
      console.log(id, PAGE_OPTIONS.id)
      if (id == PAGE_OPTIONS.id) {
        this.setActiveTab("position")
      } else {
        ROUTER.navigateTo({
          path: "/pages/notice/detail/index",
          query: {
            id,
          },
        })
      }
    },
    // 计算公告弹窗筛选弹窗弹出位置
    calculateSelectBoxPopupPosition() {
      const query = this.createSelectorQuery()
      query
        .select(".select-box")
        .boundingClientRect((rect) => {
          if (rect) {
            // 计算弹窗应该出现的位置
            const selectBoxPopupTop = rect.bottom + 10 // 在 select-box 下方 10px 处
            this.setData({
              selectBoxPopupTop,
            })
          }
        })
        .exec()
    },
    // 计算考试列表弹窗筛选弹窗弹出位置
    calculateExamPopupMenuPosition() {
      setTimeout(() => {
        console.log("开始计算exam popup位置...")
        const query = this.createSelectorQuery()

        // 获取popu-box和popu-menu .top的位置信息
        query.select(".popu-box").boundingClientRect()
        query.select(".popu-box .popu-menu .top").boundingClientRect()

        query.exec((res) => {
          const popuBoxRect = res[0]
          const topRect = res[1]

          console.log("popu-box元素信息:", popuBoxRect)
          console.log("popu-menu内部top元素信息:", topRect)

          if (popuBoxRect && topRect) {
            // 获取屏幕高度，计算20vh的像素值
            const systemInfo = wx.getSystemInfoSync()
            const screenHeight = systemInfo.windowHeight
            const vh20 = (screenHeight * 20) / 100 // 20vh转换为像素

            // 计算top元素相对于popu-box的位置
            const topRelativeToPopuBox = topRect.top - popuBoxRect.top

            // 地区选择弹窗应该出现的位置：top元素在popu-box内的位置 + top元素高度 + 20vh + 10px间距
            const examRegionPopupTop =
              topRelativeToPopuBox + topRect.height + vh20 + 10

            console.log("位置计算信息:", {
              popuBoxTop: popuBoxRect.top,
              topElementTop: topRect.top,
              topRelativeToPopuBox: topRelativeToPopuBox,
              topHeight: topRect.height,
              screenHeight: screenHeight,
              vh20: vh20,
              calculatedTop: examRegionPopupTop,
            })

            this.setData({
              examRegionPopupTop,
            })
          } else {
            console.log("未找到.popu-box或.popu-box .popu-menu .top元素")
            console.log("popuBoxRect:", popuBoxRect)
            console.log("topRect:", topRect)

            // 降级方案：使用相对安全的默认值
            const systemInfo = wx.getSystemInfoSync()
            const screenHeight = systemInfo.windowHeight
            const vh20 = (screenHeight * 20) / 100
            const fallbackTop = 100 + vh20 + 10 // 估算的相对位置

            console.log("使用降级方案，设置默认位置:", fallbackTop)
            this.setData({
              examRegionPopupTop: fallbackTop,
            })
          }
        })
      }, 300) // 确保van-popup已完全显示并渲染
    },
    // 关闭popu弹窗
    onClose() {
      this.setData({
        show: false,
        showExamRegionPopup: false,
      })
    },
    // 关闭考试列表popu
    closeExamRegionPopup() {
      this.setData({
        showExamRegionPopup: false,
        activeExamExpanded: "",
      })
    },
    // 考试弹窗
    // getNoticeSelectFromMenuData(serverMenuList) {
    //   const result = {}

    //   serverMenuList.forEach((menu) => {
    //     const filterKey = menu.filter_key

    //     if (filterKey === "filter_list") {
    //       result[filterKey] = {}

    //       menu.data.forEach((category) => {
    //         if (category.list) {
    //           category.list.forEach((filterGroup) => {
    //             const groupFilterKey = filterGroup.filter_key
    //             if (groupFilterKey) {
    //               // 直接将所有筛选项提取到 filter_list 下，去掉中间层级
    //               result[filterKey][groupFilterKey] = []
    //             }
    //           })
    //         }
    //       })
    //     } else if (filterKey) {
    //       result[filterKey] = []
    //     }
    //   })
    //   if (this.data.noticeData.detail.child_article_list.length > 0) {
    //     result["article_list"] = [
    //       this.data.noticeData.detail.child_article_list[0],
    //     ]
    //   }

    //   return result
    // },
    getNoticeSelectFromMenuData(serverMenuList) {
      const result = {}
      serverMenuList.forEach((menu) => {
        const filterKey = menu.filter_key

        if (filterKey === "filter_list") {
          result[filterKey] = {}

          menu.data.forEach((category) => {
            if (category.list) {
              category.list.forEach((filterGroup) => {
                const groupFilterKey = filterGroup.filter_key
                if (groupFilterKey) {
                  // 直接将所有筛选项提取到 filter_list 下，去掉中间层级
                  result[filterKey][groupFilterKey] = []
                }
              })
            }
          })
        } else if (filterKey) {
          result[filterKey] = []
        }
        // if (filterKey === "filter_list") {
        //   result[filterKey] = {}
        //   menu.data.forEach((filterGroup) => {
        //     const groupFilterKey = filterGroup.filter_key
        //     if (groupFilterKey) {
        //       result[filterKey][groupFilterKey] = []
        //     }
        //   })
        // } else if (filterKey) {
        //   result[filterKey] = []
        // }
        if (
          this.data?.noticeData?.detail?.child_article_list?.length > 0 &&
          this.data.pageType == "detail"
        ) {
          result["article_list"] = [
            this.data.noticeData.detail.child_article_list[0],
          ]
        }
      })
      console.log(result, "12222222222222")
      return result
    },
    // 考试列表弹窗相关逻辑
    async openExam() {
      await this.updateJobPopuSelectForTemplateFromCache()
      this.applyPopuFilter()
      this.setData(
        {
          show: true,
        },
        () => {
          // 在setData回调中执行，确保DOM已更新
          // 获取 popu-menu 的位置信息
          this.calculateExamPopupMenuPosition()
        }
      )
    },
    // 获取考试列表弹窗相关逻辑
    async getChildListForDetail(filterConditions = {}) {
      const requestParams = {
        ...filterConditions,
        article_id: this.PAGE_OPTIONS.id,
      }
      const res = await UTIL.request(API.getChildListForDetail, requestParams)
      if (res && res.error && res.error.code === 0 && res.data) {
        this.setData({
          examData: res.data,
        })
        if (res.data.filter_menu.length) {
          this.initexamPopuMenu(res.data.filter_menu)
        }
      }
    },
    // 考试列表弹窗筛选相关逻辑
    initexamPopuMenu(serverMenuList) {
      if (serverMenuList && serverMenuList.length) {
        // 使用提取后的纯函数处理菜单
        const menuList = processMenuList(serverMenuList)
        const examMenuData = {}
        serverMenuList.forEach((item) => {
          examMenuData[item.filter_key] = item
        })

        this.setData({
          examMenuData,
          examList: menuList,
          examPopuSelectForTemplate:
            this.data?.examPopuSelectForTemplate?.apply_status?.length > 0
              ? this.data.examPopuSelectForTemplate
              : this.getExamPopuSelectFromMenuData(serverMenuList),
        })
      }
    },
    // 初始化考试列表弹窗筛选状态
    getExamPopuSelectFromMenuData(serverMenuList) {
      const result = {}
      serverMenuList.forEach((menu) => {
        const filterKey = menu.filter_key
        console.log(filterKey, "---------------------------------------------")
        if (filterKey === "filter_list") {
          result[filterKey] = {}

          menu.data.forEach((category) => {
            if (category.list) {
              category.list.forEach((filterGroup) => {
                const groupFilterKey = filterGroup.filter_key
                if (groupFilterKey) {
                  // 直接将所有筛选项提取到 filter_list 下，去掉中间层级
                  result[filterKey][groupFilterKey] = []
                }
              })
            }
          })
        } else if (filterKey) {
          result[filterKey] = []
        }
        if (filterKey == "apply_status") {
          result[filterKey] = [2]
        }
      })
      return result
    },
    // 考试列表弹窗筛选项点击
    handleExamMenuClick(e) {
      const { type, currentItem } = e.detail || e.currentTarget.dataset
      const { showExamRegionPopup, examPopuSelectForTemplate } = this.data
      const filterKey = currentItem.filter_key
      console.log(currentItem, "----------------------------")
      const currentMenuSelected = examPopuSelectForTemplate[filterKey]

      if (
        type === "apply_region" ||
        type == "apply_status" ||
        type == "sort_time"
      ) {
        this.hidePopupMenu()
      } else if (
        showExamRegionPopup === true &&
        this.data.activeExamExpanded === filterKey
      ) {
        this.hidePopupMenu()
      } else {
        console.log("进的这里？")
        this.setData({
          showExamRegionPopup: true,
        })
      }
      this.setData({
        activeExamExpanded:
          this.data.activeExamExpanded == filterKey ? "" : filterKey, // 设置当前key为展开状态
      })

      // 处理 filter_key 为 apply_region 的情况：走公告现在的逻辑
      if (filterKey === "apply_status") {
        // 切换地区列表显示状态
        this.setData({
          showApplyStatus: !this.data.showApplyStatus,
        })
        return
      }

      // 处理sort_time类型：单选，不打开弹窗，点击选中，再次点击取消
      if (type === "sort_time") {
        const currentValue = currentMenuSelected
        let newValue
        if (!currentValue || currentValue.length === 0) {
          // 没有值，设置为1
          newValue = [1]
        } else if (currentValue[0] == 1) {
          // 值为1，设置为2
          newValue = [2]
        } else if (currentValue[0] == 2) {
          // 值为2，设置为1
          newValue = [1]
        } else {
          // 其他情况，默认设置为1
          newValue = []
        }
        console.log(newValue, "拿到的")

        this.setData({
          [`examPopuSelectForTemplate.${filterKey}`]: newValue,
        })
        setJobDetailPopuSelectForTemplateCache(
          this.data.examPopuSelectForTemplate
        )
        this.applyPopuFilter()
        return
      }
    },
    // 获取考试列表弹窗筛选缓存
    updateJobPopuSelectForTemplateFromCache() {
      const examPopuSelectForTemplate = getJobDetailPopuSelectForTemplateCache()
      console.log(examPopuSelectForTemplate)
      this.setData({
        examPopuSelectForTemplate,
      })
    },

    // 考试列表弹窗应用筛选请求
    async applyPopuFilter() {
      const apiParams = this.buildApiParams(this.data.examPopuSelectForTemplate)
      console.log("拿到的参数", apiParams)
      await this.getChildListForDetail(apiParams)
    },
    // 考试列表弹窗地区筛选确认弹窗
    handlePopuRegionSelection(e) {
      const { filterKey, tempSelected } = e.detail
      console.log(filterKey, tempSelected)
      this.setData({
        [`examPopuSelectForTemplate.${filterKey}`]: tempSelected,
        activeExamExpanded: "",
      })
      setJobDetailPopuSelectForTemplateCache(
        this.data.examPopuSelectForTemplate
      )
      this.applyPopuFilter()
      this.hidePopupMenu()
    },
    // 关闭考试列表里面筛选项的弹窗
    hidePopupMenu() {
      this.setData({
        showExamRegionPopup: false,
      })
    },

    // 详情tab初始化
    generateDynamicTabs() {
      const { noticeData } = this.data
      if (!noticeData || !noticeData.detail) {
        console.log("公告数据未加载，使用默认tab配置")
        return
      }

      const detail = noticeData.detail
      const dynamicTabs = []
      const tabKeyToIndex = {}
      const indexToTabKey = {}

      this.data?.allTabsConfig.forEach((tabConfig) => {
        const { key, title, alwaysShow, conditionField } = tabConfig

        // 总是显示的tab或满足条件的tab
        if (alwaysShow || (conditionField && detail[conditionField] > 0)) {
          const index = dynamicTabs.length
          dynamicTabs.push(title)
          tabKeyToIndex[key] = index
          indexToTabKey[index] = key
        }
      })

      console.log("动态生成的tabs:", {
        tabs: dynamicTabs,
        keyToIndex: tabKeyToIndex,
        indexToKey: indexToTabKey,
        conditions: {
          job_num: detail.job_num,
          notice_num: detail.notice_num,
        },
      })

      this.setData({
        tabList: dynamicTabs,
        tabKeyToIndex,
        indexToTabKey,
      })
    },
    // 切换tab
    changeTab(e) {
      console.log(e.currentTarget.dataset.index, e, "-----------------")
      const { index, item } = e.currentTarget.dataset
      // 如果有弹窗打开，先关闭弹窗
      if (this.data.showPopupFilterMenu) {
        this.hidePopupMenuFilter()
      }
      this.setData({
        activeIndex: index,
      })
      if (this.data.indexToTabKey[this.data.activeIndex] == "position") {
        this.applyFilter()
      } else if (this.data.indexToTabKey[this.data.activeIndex] == "official") {
        // 记录点击官方动态的时间
        this.recordOfficialTabClickTime()
        this.getArticleNoticeList()
      }
    },
    // 记录官方动态tab点击时间
    recordOfficialTabClickTime() {
      console.log(123123123123, "进来没得")
      const noticeId = this.data.noticeData?.detail?.id
      if (!noticeId) return

      // 获取当前时间戳
      const currentTime = Date.now()

      // 从缓存获取已有的记录对象
      let officialClickRecord = getOfficialNewsCache() || {}

      // 更新或添加当前公告的点击时间（相同键直接覆盖）
      officialClickRecord[noticeId] = currentTime

      // 保存回缓存
      setOfficialNewsCache(officialClickRecord)

      console.log("记录官方动态点击时间:", {
        noticeId,
        currentTime,
        allRecords: officialClickRecord,
      })
    },
    // 获取富文本高度
    checkContentHeight() {
      console.log("进来没得")
      // 延迟执行，确保rich-text内容已经渲染
      let retryCount = 0
      const maxRetries = 3
      this.checkHeight(retryCount, maxRetries)
    },
    checkHeight(retryCount, maxRetries) {
      const query = wx.createSelectorQuery().in(this)
      query.select("#richTextContent").boundingClientRect()
      query.exec((res) => {
        console.log("进来没得1")
        if (res && res[0] && res[0].height > 0) {
          const contentHeight = res[0].height
          const needToggle = contentHeight > this.data.maxHeight

          console.log("Rich-text content height:", contentHeight)
          console.log("Max height:", this.data.maxHeight)
          console.log("Need toggle:", needToggle)

          this.setData({
            actualHeight: contentHeight,
            showToggle: needToggle,
            // 如果内容高度小于等于maxHeight，默认展开
            isExpanded: !needToggle,
          })
        } else if (retryCount < maxRetries) {
          // // 如果高度获取失败，进行重试
          // retryCount++
          // console.log(
          //   `Rich-text高度检测失败，正在重试 ${retryCount}/${maxRetries}`
          // )
          // setTimeout(checkHeight, 300)
        } else {
          // 重试失败，设置默认状态
          console.warn("Rich-text高度检测失败，使用默认状态")
          this.setData({
            showToggle: false,
            isExpanded: true,
          })
        }
      })
    },
    // 点击附件
    tapAttachment(e) {
      const val = e.currentTarget.dataset.item
      const url = val.url
      const name = val.name
      wx.showLoading({
        title: "",
        mask: true,
      })
      APP.openFile(url, { fileName: name })
        .then((res) => {
          console.log("文件打开成功", url)
          wx.hideLoading()
        })
        .catch((res) => {
          console.log(res)
          wx.hideLoading()
          // wx.navigateTo({
          //   url: `/pages/file-preview/index?filePath=${res.filePath}&fileName=${res.fileName}`,
          // })
        })
    },
    tapCustomerService(e) {
      const { cmd_json } = e.currentTarget.dataset.item
      console.log(e, cmd_json)
      APP.toCmdUnitKey(cmd_json)
    },
    // 复制链接
    copyUrl(e) {
      const link = e.currentTarget.dataset.item

      // 调用微信API复制链接到剪贴板
      wx.setClipboardData({
        data: link,
        success: () => {
          wx.showToast({
            title: "复制成功",
            duration: 2000,
            icon: "none",
          })
        },
      })
    },
    // 去简历
    goResume() {
      ROUTER.navigateTo({
        path: "/pages/my/resume/index",
      })
    },
    // 页面返回方法
    backPage() {
      if (getCurrentPages().length <= 1) {
        wx.reLaunch({
          url: "/pages/home/<USER>/index",
        })
        console.log("回首页")
        return false
      }
      console.log("触发返回")
      wx.navigateBack({
        delta: 1,
      })
    },
    // 去职位筛选
    goSelect() {
      ROUTER.navigateTo({
        path: "/pages/select/select-job/index",
        query: {
          type: "detail",
          article_id: this.PAGE_OPTIONS.id,
        },
      })
    },
    // 去搜索页
    goSearch() {
      ROUTER.navigateTo({
        path: "/pages/search/index",
        query: {
          type: 1,
          placeholder: "搜索职位职位代码为关键词",
        },
      })
    },
    // 展开收起
    toggleText() {
      this.setData({
        isExpanded: !this.data.isExpanded,
      })
    },

    toNewsDetail(e) {
      const { id } = e.currentTarget.dataset.item
      ROUTER.navigateTo({
        path: "/pages/notice/dynamics/index",
        query: {
          id,
        },
      })
    },
    // 切换tab
    changeActiveIndex(e) {
      const targetTabKey = e.currentTarget.dataset.item

      // 兼容旧的数字格式和新的字符串格式
      if (typeof targetTabKey === "string" && !isNaN(targetTabKey)) {
        // 如果是数字字符串，转换为数字
        const index = parseInt(targetTabKey)
        this.setData({ activeIndex: index })
      } else if (typeof targetTabKey === "string") {
        // 如果是tab类型字符串，使用新的映射方法
        this.setActiveTab(targetTabKey)
      } else if (typeof targetTabKey === "number") {
        // 如果是数字，直接设置
        this.setData({ activeIndex: targetTabKey })
      } else {
        console.warn("未知的tab类型:", targetTabKey)
      }
    },
    setActiveTab(tabKey) {
      const { tabKeyToIndex } = this.data
      const targetIndex = tabKeyToIndex[tabKey]
      console.log(tabKey, "12312312312312")
      if (targetIndex !== undefined) {
        this.setData({ activeIndex: targetIndex })
        return true
      }
      if (tabKey == "position") {
        this.applyFilter()
      }

      console.warn(`Tab类型 ${tabKey} 不存在于当前tab列表中`)
      return false
    },
    // 获取吸顶高度
    setMenuStickyTop() {
      const query = wx.createSelectorQuery()
      query
        .select("#commonHeader")
        .boundingClientRect((headerRect) => {
          let headerHeight = 100 // 默认高度
          console.log("获取到的header信息:", headerRect)

          if (headerRect) {
            headerHeight = headerRect.height
            console.log("成功获取导航栏高度:", headerHeight)
          } else {
            // 降级方案：通过系统信息计算
            const systemInfo = wx.getSystemInfoSync()
            const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
            headerHeight = menuButtonInfo.bottom
          }

          // 获取content-box-top的高度
          const contentTopQuery = wx.createSelectorQuery()
          contentTopQuery
            .select(".content-box-top")
            .boundingClientRect((contentTopRect) => {
              let contentTopHeight = 120 // 默认高度

              if (contentTopRect) {
                contentTopHeight = contentTopRect.height
                console.log("成功获取content-box-top高度:", contentTopHeight)
              } else {
                console.log("无法获取content-box-top高度，使用默认值")
              }

              // 计算select-box-content的top值
              const selectBoxTop = headerHeight + contentTopHeight
              console.log("计算得出的select-box-content top值:", selectBoxTop)

              // 设置数据
              this.setData({
                headerHeight: headerHeight - 15,
                contentBoxTopHeight: contentTopHeight,
                selectBoxTop: selectBoxTop - 15,
              })

              console.log("导航栏高度:", headerHeight)
              console.log("content-box-top高度:", contentTopHeight)
              console.log("select-box-content top值:", selectBoxTop)
            })
            .exec()
        })
        .exec()
    },
    handlePopupClose() {
      console.log("弹窗关闭事件")
      // 清空展开状态
      this.setData({
        activeExpanded: "",
      })
      // 调用hidePopupMenuFilter真正关闭弹窗（包含恢复备份的逻辑）
      this.hidePopupMenuFilter()
    },
    onPopuClose() {
      this.setData({
        popuShow: false,
      })
    },
    onConfirm(e) {
      this.onPopuClose()
      const data = e.detail.value
      const path = "/pages/notice/collection/index"
      ROUTER.navigateTo({
        path,
        query: {
          id: data.id,
        },
      })
    },
    openPopu() {
      this.setData({
        popuShow: true,
      })
    },
    majorConfirm(e) {
      console.log(e.detail, "-------------------")
      const selected = {
        educationId: e.detail.educationId,
        isSync: e.detail.isSync,
        selectedMajorIds: e.detail.selectedMajorIds,
      }
      this.setData({
        [`jobDetailSelectForTemplate.tmp_major`]: selected,
        majorShow: false,
        activeExpanded: "",
      })
      console.log(this.data.jobDetailSelectForTemplate, "1231233333333333")
      setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
      this.hidePopupMenuFilter()
      this.closeSelectBoxPopup()
      this.applyFilter()
      // const { filterKey, tempSelected } = e.detail
      // // 清空展开状态
      // this.setData({
      //   [`jobDetailSelectForTemplate.${filterKey}`]: tempSelected,
      //   activeExpanded: "",
      // })
      // setJobDetailSelectForTemplateCache(this.data.jobDetailSelectForTemplate)
      // this.hidePopupMenuFilter()
      // this.closeSelectBoxPopup()
      // this.applyFilter()
    },
    majorClose() {
      this.setData({
        majorShow: false,
      })
    },
  },

  /**
   * 初始化 Mixin
   * 在页面的 onLoad 或组件的 attached 中调用
   */
  initNoticeMixin() {
    // 将地区数据合并到页面数据中
    const currentData = this.data || {}
    this.setData({
      ...currentData,
      ...this.noticeData,
    })

    // 将地区方法合并到页面方法中
    Object.assign(this, noticeMixin.noticeMethods)

    console.log("noticeMixin 初始化完成")
  },

  /**
   * 使用示例：
   *
   * 1. 在页面中导入并混入 mixin：
   *    const RegionSelectMixin = require("@/services/regionSelectMixin")
   *    const pageConfig = Object.assign({}, RegionSelectMixin, {
   *      // 页面配置
   *    })
   *
   * 2. 在 onLoad 中初始化：
   *    this.initRegionSelectMixin()
   *
   * 3. 初始化地区数据：
   *    this.initRegionData('announcement', 'regionPopupData') // 公告Tab
   *    this.initRegionData('news', 'newsRegionPopupData')     // 考试动态Tab
   *
   * 4. 创建事件处理器：
   *    const handlers = this.createRegionHandlers('regionPopupData', 'announcement', (regions) => {
   *      // 确认回调
   *      this.updateRegionSelectState(regions, 'noticeSelectForTemplate', 'apply_region')
   *    })
   *
   * 5. 在页面方法中使用：
   *    handleRegionProvinceClick(e) {
   *      this.handleProvinceClick(e, 'regionPopupData')
   *    }
   */
}

module.exports = noticeMixin
