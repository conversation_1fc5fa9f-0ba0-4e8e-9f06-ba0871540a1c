<wxs module="utils" src="./index.wxs"></wxs>
<view class="job-detail">
  <view class="top-area">
    <view class="title-area">
      <view class="left-text">{{detailData.name}}</view>
      <view class="right-text" style="color: {{detailData.apply_status.color}};">{{detailData.apply_status.text}}</view>
    </view>
    <view class="main-area">
      <view class="entry-item">
        <view class="title">招录人数</view>
        <view class="content">{{detailData.need_num}}人</view>
      </view>
      <view class="entry-item">
        <view class="title">招录单位</view>
        <view class="content">{{detailData.work_unit}}</view>
      </view>
    </view>
  </view>
  <report-card class="baokao-area" title="报考数据" isHaveRight="1" isNeedRightAngle="{{ true }}">
    <view class="data-area">
      <view class="data-item">
        <view class="content">
          <view class="num red">{{detailData.job_data_record.apply_num || '-'}}</view>
          <view class="text">报名人数</view>
        </view>
        <view class="line"></view>
      </view>
      <view class="data-item">
        <view class="content">
          <view class="num orange">{{detailData.job_data_record.approved_num || '-'}}</view>
          <view class="text">过审人数</view>
        </view>
        <view class="line"></view>
      </view>
      <view class="data-item">
        <view class="content">
          <view class="num orange">{{detailData.job_data_record.pay_num || '-'}}</view>
          <view class="text">缴费人数</view>
        </view>
        <view class="line"></view>
      </view>
      <view class="data-item">
        <view class="content">
          <view class="num">{{detailData.job_data_record.min_score || '-'}}</view>
          <view class="text">最低进面分</view>
        </view>
        <view class="line"></view>
      </view>
    </view>
    <view class="data-sourse">
      <view class="list-item">
        <view class="title">数据来源：</view>
        <view class="text">{{detailData.job_data_record.release_source}}</view>
      </view>
    </view>
  </report-card>
  <report-card class="history-area" title="历年数据" isNeedRightAngle="{{ true }}">
    <view class="card-img" wx:if="{{false}}">
      <image class="bg-img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/history-bg.png" mode="" />
      <view class="title">
        四川省考历年进面分汇总
      </view>
      <view class="btn">添加客服领取</view>
    </view>
    <view wx:else>
      <view class="position-sub-tab-list">
        <view class="position-sub-tab-item {{selectedSubTabs === 0 ? 'active' : ''}}" data-index="0" bindtap="onSubTabClick">
          相似职位
        </view>
        <view class="position-sub-tab-item {{selectedSubTabs === 1 ? 'active' : ''}}" data-index="1" bindtap="onSubTabClick">
          所属单位
        </view>
        <view class="position-sub-tab-item {{selectedSubTabs === 2 ? 'active' : ''}}" data-index="2" bindtap="onSubTabClick">
          所属地区
        </view>
      </view>
      <view class="position-table-content">
        <!-- 表格头部 -->
        <view class="position-table-header">
          <view class="header-item header-1">
            <text>年份</text>
          </view>
          <view class="header-item header-2">
            <text>招聘</text>
            <text>人数</text>
          </view>
          <view class="header-item header-3">
            <text>报名</text>
            <text>人数</text>
          </view>
          <view class="header-item header-4">
            <text>报录</text>
            <text>比</text>
          </view>
          <view class="header-item header-5">
            <text>最低进</text>
            <text>面分</text>
          </view>
        </view>

        <!-- 表格数据 -->
        <view class="position-table-body">
          <view wx:for="{{selectedSubTabs == 1?detailData.history_data.belong_unit.list:selectedSubTabs == 2?detailData.history_data.belong_area.list:detailData.history_data.similar_job.list}}" wx:key="index" class="position-table-row">
            <view class="table-item table-1">
              <text class="year-text">{{item.year}}</text>
            </view>
            <view class="table-item table-2">
              <text class="data-text">{{item.need_num}}</text>
            </view>
            <view class="table-item table-3">
              <text class="data-text">{{item.apply_num}}</text>
            </view>
            <view class="table-item table-4">
              <text class="data-text">{{item.apply_accept_rate}}</text>
            </view>
            <view class="table-item table-5">
              <text class="data-text">{{item.min_score_for_interview}}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="desc-text"> <text class="gray-text">说明：</text>{{ selectedSubTabs == 1?detailData.history_data.belong_unit.note:selectedSubTabs == 2?detailData.history_data.belong_area.note:detailData.history_data.similar_job.note }}</view>
    </view>
  </report-card>
  <report-card title="职位信息" class="form-area">
    <view class="form-item" wx:for="{{ detailData.job_info }}" wx:key="index">
      <view class="left-text">{{item.title}}</view>
      <view class="right-text">{{item.value || '-'}}</view>
    </view>
  </report-card>
  <report-card title="招考条件" class="form-area">
    <view class="form-item" wx:for="{{ detailData.apply_condition }}" wx:key="index">
      <view class="left-text">{{item.title}}</view>
      <view class="right-text">{{item.value || '-'}}</view>
    </view>
  </report-card>
  <report-card title="其它信息" class="form-area" wx:if="{{detailData.other_info && detailData.other_info.length}}">
    <view class="form-item" wx:for="{{detailData.other_info}}" wx:key="index">
      <view class="left-text">{{item.title}}</view>
      <view class="right-text">{{item.value || '-'}}</view>
    </view>
  </report-card>
  <view class="bottom-text">
    声明：本站发布的招考资讯均来源于招录官方网站，由金标尺整理编辑，如遇报考疑问请咨询招考官方，若涉及版权或错误信息，请提交反馈到本站予以更新或删除。
  </view>
  <tabbar-box>
    <view class="bottom-area">
      <button-authorize isBindPhone="{{isLogin}}" bind:onAuthorize="goComparison">
        <view class="left">
          <view class="img-area">
            <image class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/{{isLogin && pkListIds.length > 0?'pk':'pk_black'}}.png" mode="" />
            <view class="message" wx:if="{{isLogin && pkListIds.length}}">{{pkListIds.length}}</view>
          </view>
          <view class="text">加入对比</view>
        </view>
      </button-authorize>
      <view class="right">
        <view class="btn_1" bind:tap="goNoticeDetail">公告详情</view>
        <button-authorize isBindPhone="{{isLogin}}" bind:onAuthorize="setFocus">
          <view class="btn_2 {{detailData.is_follow == 1?'op08':''}}">
            <image wx:if="{{detailData.is_follow == 0}}" class="img" src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_focus.png" mode="" />
            {{detailData.is_follow == 1?'取消关注':'关注职位'}}
          </view>
        </button-authorize>
      </view>
    </view>
  </tabbar-box>
</view>