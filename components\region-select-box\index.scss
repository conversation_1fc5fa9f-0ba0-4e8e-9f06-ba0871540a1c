.select-top {
  padding: 24rpx 160rpx 24rpx 32rpx;
  position: relative;
  border-bottom: 1rpx solid rgba(235, 236, 240, 1);
  background: #fff;
  display: flex;
  align-items: center;
  // padding-bottom: 0;
  // padding-top: 0;
  box-sizing: border-box;
  &.bg {
    background: linear-gradient(to bottom, #f7f8fa, #fff);
    .clear-box {
      background: linear-gradient(to bottom, #f7f8fa, #fff);
    }
  }
  &.pd0 {
    // padding-top: 8rpx;
  }
}

.select-top .select-list {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.select-top .select-list-item {
  font-size: 22rpx;
  color: rgba(230, 0, 3, 1);
  background: rgba(230, 0, 3, 0.05);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx 0 24rpx;
  height: 52rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
}

.select-top .select-list-item .close {
  width: 24rpx;
  height: 24rpx;
  margin-left: 16rpx;
  transform: translateY(3rpx);
}

.select-top .clear-box {
  position: absolute;
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: rgba(60, 61, 66, 1);
  background: #fff;
  top: 0;
  right: 0;
  height: 100%;
  padding: 0 32rpx 0 44rpx;
  // background: linear-gradient(to bottom, #f7f8fa, #fff);
  &.hui {
    color: rgba(60, 61, 66, 0.5);
  }
}

.select-top .clear-box::after {
  display: block;
  content: " ";
  width: 2rpx;
  height: 64rpx;
  background: rgba(235, 236, 240, 1);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 22rpx;
}

.select-top .clear-box .img {
  width: 32rpx;
  height: 32rpx;
  transform: translateY(1rpx);
  margin-right: 4rpx;
}

.select-box {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.select-box.popup-mode {
  height: auto;
}

.select-center {
  flex: 1;
  min-height: 0;
  display: flex;
}

.select-center-item {
  width: 33.33333%;
  position: relative;
}

/* 二级选择时的布局：第一级33.333%，第二级66.667% */
.select-box[data-select-level="2"] .select-center-item:nth-child(1) {
  width: 33.333%;
}

.select-box[data-select-level="2"] .select-center-item:nth-child(2) {
  width: 66.667%;
}

.select-center-item:last-child::after {
  display: none;
}

.select-center-item::after {
  position: absolute;
  content: "";
  display: block;
  width: 1rpx;
  height: 100%;
  background: rgba(235, 236, 240, 1);
  right: 1rpx;
  top: 0;
}

.province {
  font-size: 26rpx;
  padding: 24rpx 16rpx 24rpx 32rpx;
  background: rgba(247, 248, 250, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.province .num {
  min-width: 32rpx;
  min-height: 32rpx;
  background: rgba(230, 0, 3, 0.1);
  font-size: 20rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(230, 0, 3, 1);
  font-family: "DINGBold";
}

.province .num.active {
  background: rgba(230, 0, 3, 1);
  color: #fff;
}

.province.active {
  background: #fff;
  position: relative;
}

.province.active::after {
  display: block;
  position: absolute;
  content: " ";
  width: 1rpx;
  height: 100%;
  background-color: #fff;
  top: 0;
  right: 1rpx;
  z-index: 2;
}

.city {
  font-size: 26rpx;
  color: rgba(60, 61, 66, 1);
  padding: 24rpx 16rpx 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.city.active {
  font-weight: bold;
  color: rgba(230, 0, 3, 1);
}

.city .num {
  min-width: 32rpx;
  min-height: 32rpx;
  background: rgba(230, 0, 3, 0.1);
  font-size: 20rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(230, 0, 3, 1);
  font-family: "DINGBold";
}

.city .num.active {
  background: rgba(230, 0, 3, 1);
  color: #fff;
}

.city .img {
  width: 40rpx;
  height: 40rpx;
}

.region {
  padding: 24rpx 16rpx 24rpx 32rpx;
  font-size: 26rpx;
  color: rgba(60, 61, 66, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.region.active {
  color: rgba(230, 0, 3, 1);
  font-weight: bold;
}

.region .img {
  width: 40rpx;
  height: 40rpx;
}

.loading-text,
.empty-text {
  padding: 60rpx 24rpx;
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

.empty-text {
  color: #ccc;
}

/* 修复弹窗中的按钮定位问题 */
.action-bar-box {
  position: relative;
  height: 142rpx;
  z-index: 998;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  background-color: #fff;
}

.action-bar {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-sizing: border-box;
  padding: 24rpx 32rpx 34rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.confirm-btn {
  background: rgba(236, 62, 51, 1);
  font-size: 28rpx;
  color: #fff;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

/* 通用样式 */
.container {
  max-width: 750rpx;
  margin: 0 auto;
}

.flex-justify_between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.no-text {
  font-size: 26rpx;
  color: rgba(194, 197, 204, 1);
  height: 52rpx;
  display: flex;
  align-items: center;
}

.disable-box {
  display: flex;
  align-items: center;
}

.stepbtn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: rgba(236, 62, 51, 1);
  background: rgba(236, 62, 51, 0.1);
  border-radius: 16rpx;
  width: 250rpx;
  height: 84rpx;
  margin-right: 24rpx;
}
