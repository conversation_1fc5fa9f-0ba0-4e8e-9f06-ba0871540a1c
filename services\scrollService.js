/**
 * 滚动位置管理服务
 * 提供页面滚动位置的记录、恢复功能
 *
 * 功能特性：
 * - 简洁的API设计，只传递key和滚动位置
 * - 支持多页面独立管理滚动位置
 * - 内存存储，页面生命周期内有效
 * - 最小侵入性集成
 * - 支持吸顶高度管理，智能处理tab切换滚动逻辑
 */

// 内存存储滚动位置数据
const scrollPositions = {}

// 内存存储吸顶高度配置
let stickyTop = 0

// 内存存储当前滚动位置（用于智能恢复时的临时存储）
let currentScrollPosition = 0

/**
 * 记录滚动位置
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} scrollTop 滚动位置
 */
function setScrollPosition(key, scrollTop) {
  scrollPositions[key] = scrollTop
  setCurrentScrollPosition(scrollTop)
}

/**
 * 智能恢复滚动位置（支持吸顶高度管理）
 * @param {string} key 目标位置标识
 * @param {Object} options 恢复选项
 * @param {number} options.duration 动画时长，默认0（无动画）
 * @param {string} options.currentKey 当前位置标识（可选，用于获取当前滚动状态）
 */
function restoreScrollPosition(key, options = {}) {
  const { duration = 0, currentKey } = options

  // 获取当前滚动位置
  let currentScroll = getCurrentScrollPosition()
  let stickyTop = getStickyTop()

  const targetSavedScroll = getScrollPosition(key)

  let targetScrollTop = 0

  console.log(key, scrollPositions, currentScroll, targetSavedScroll, stickyTop)

  // 应用滚动高度管理规则
  if (currentScroll < stickyTop) {
    // 规则1：当前tab滚动高度 < 吸顶高度时，下一个tab滚动到当前tab的实际滚动高度
    targetScrollTop = currentScroll
  } else {
    // 规则2：当前tab滚动高度 ≥ 吸顶高度时
    if (targetSavedScroll >= stickyTop) {
      targetScrollTop = targetSavedScroll
    } else {
      // 没有保存的高度：滚动到吸顶高度
      targetScrollTop = stickyTop
    }
  }
  console.log(targetScrollTop, targetSavedScroll)

  wx.pageScrollTo({
    scrollTop: targetScrollTop,
    duration,
  })
}

/**
 * 获取滚动位置
 * @param {string} key 位置标识
 * @returns {number} 滚动位置
 */
function getScrollPosition(key) {
  return scrollPositions[key] || 0
}

/**
 * 设置吸顶高度
 * @param {string} key 位置标识，格式：'pageKey' 或 'pageKey-tabKey'
 * @param {number} stickyTop 吸顶高度
 */
function setStickyTop(stickyTop) {
  stickyTop = stickyTop
}

/**
 * 获取吸顶高度
 * @param {string} key 位置标识
 * @returns {number} 吸顶高度
 */
function getStickyTop() {
  return stickyTop || 0
}

/**
 * 设置当前滚动位置（用于智能恢复）
 * @param {number} scrollTop 当前滚动位置
 */
function setCurrentScrollPosition(scrollTop) {
  currentScrollPosition = scrollTop
}

/**
 * 获取当前滚动位置
 * @returns {number} 当前滚动位置
 */
function getCurrentScrollPosition() {
  return currentScrollPosition
}

/**
 * 清除滚动位置
 * @param {string} key 位置标识，不传则清除所有
 */
function clearScrollPosition(key = null) {
  if (key === null) {
    // 清除所有滚动位置
    Object.keys(scrollPositions).forEach((k) => {
      delete scrollPositions[k]
    })
  } else {
    // 清除指定位置
    delete scrollPositions[key]
  }
}

module.exports = {
  setScrollPosition,
  getScrollPosition,
  restoreScrollPosition,
  clearScrollPosition,
  setStickyTop,
}
